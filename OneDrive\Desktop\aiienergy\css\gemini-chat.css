/**
 * Enhanced Gemini-style Chat Interface
 * واجهة المحادثة المحسنة بنمط Gemini
 */

.gemini-chat-modal {
    position: fixed;
    inset: 0;
    background: #0d1117;
    z-index: 10002;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.gemini-chat-modal.open {
    opacity: 1;
    pointer-events: auto;
}

.gemini-chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    position: relative;
}

/* Header Styles */
.gemini-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(10px);
}

.gemini-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.gemini-close {
    background: none;
    border: none;
    color: #7d8590;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.gemini-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e6edf3;
}

/* Messages Container - Force Center Layout */
.gemini-messages {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 24px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 24px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Welcome Screen - Enhanced Center Layout */
.gemini-welcome {
    text-align: center;
    color: #9ca3af;
    margin-top: auto;
    margin-bottom: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.gemini-welcome h2 {
    color: #58a6ff;
    font-size: 48px;
    margin-bottom: 16px;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(88, 166, 255, 0.3);
    background: linear-gradient(135deg, #58a6ff 0%, #4493e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gemini-welcome p {
    font-size: 20px;
    margin-bottom: 32px;
    color: #ffffff;
    font-weight: 400;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
}



/* Suggestion Cards - Enhanced Center Layout */
.gemini-suggestions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    max-width: 750px;
    margin: 0 auto;
    padding: 0 10px;
}

.suggestion-card {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.suggestion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(88, 166, 255, 0.5), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.suggestion-card:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(88, 166, 255, 0.4);
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.suggestion-card:hover::before {
    opacity: 1;
}

.suggestion-card h4 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.suggestion-card p {
    color: #9ca3af;
    font-size: 13px;
    line-height: 1.5;
    opacity: 0.9;
}

/* Message Bubbles - Simple Flexbox Center */
.message-bubble {
    max-width: 85%;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
    text-align: center;
    align-self: center;
    width: fit-content;
}

.message-bubble.user {
    background: rgba(88, 166, 255, 0.2);
    border: 1px solid rgba(88, 166, 255, 0.4);
    border-radius: 24px;
    padding: 16px 24px;
    color: #ffffff;
    font-weight: 500;
    box-shadow: 0 4px 20px rgba(88, 166, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.message-bubble.assistant {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    padding: 20px 28px;
    color: #e6edf3;
    line-height: 1.7;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* Simple and effective centering */
.message-bubble * {
    text-align: center;
}

.message-bubble p {
    text-align: center;
    margin: 0 auto;
}

.message-bubble ul,
.message-bubble li {
    text-align: center;
    list-style-position: inside;
}

.message-bubble strong,
.message-bubble em {
    text-align: center;
}

/* Input Container - Enhanced Center Layout */
.gemini-input-container {
    padding: 32px 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(13, 17, 23, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.gemini-input-wrapper {
    position: relative;
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gemini-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 30px;
    padding: 16px 60px 16px 24px;
    color: #ffffff;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    resize: none;
    min-height: 56px;
    max-height: 140px;
    font-family: inherit;
    text-align: right;
    direction: rtl;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.gemini-input:focus {
    border-color: rgba(88, 166, 255, 0.6);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.15), 0 8px 30px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

.gemini-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.gemini-send-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #58a6ff 0%, #4493e0 100%);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
}

.gemini-send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #4493e0 0%, #3182ce 100%);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
}

.gemini-send-btn:disabled {
    background: #374151;
    cursor: not-allowed;
    transform: translateY(-50%);
    box-shadow: none;
}

/* Typing Indicator - Flexbox Center */
.typing-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #9ca3af;
    font-size: 15px;
    padding: 20px 0;
    animation: fadeInUp 0.3s ease;
    max-width: 85%;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    align-self: center;
    width: fit-content;
}

.typing-dots {
    display: flex;
    gap: 6px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #58a6ff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
    box-shadow: 0 2px 8px rgba(88, 166, 255, 0.3);
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.4;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Input Tools - Enhanced Center Layout */
.gemini-input-tools {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
    flex-wrap: wrap;
    max-width: 700px;
    width: 100%;
}

.input-tool {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 10px 16px;
    color: #9ca3af;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.input-tool:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(88, 166, 255, 0.3);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInFromCenter {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
    }
    50% {
        box-shadow: 0 6px 25px rgba(88, 166, 255, 0.5);
    }
}

/* Apply enhanced animations */
.message-bubble {
    animation: slideInFromCenter 0.4s ease-out;
}

.gemini-send-btn:not(:disabled) {
    animation: pulseGlow 2s infinite;
}

/* Scrollbar Styling */
.gemini-messages::-webkit-scrollbar {
    width: 6px;
}

.gemini-messages::-webkit-scrollbar-track {
    background: transparent;
}

.gemini-messages::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.gemini-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design - Enhanced */
@media (max-width: 768px) {
    .gemini-chat-container {
        max-width: 100%;
    }

    .gemini-header {
        padding: 16px 20px;
    }

    .gemini-messages {
        padding: 20px 16px;
    }

    .gemini-input-container {
        padding: 24px 16px;
    }

    .gemini-input-wrapper {
        max-width: 100%;
    }

    .gemini-input {
        padding: 14px 55px 14px 20px;
        min-height: 52px;
        font-size: 15px;
    }

    .gemini-send-btn {
        width: 36px;
        height: 36px;
        right: 10px;
    }

    .gemini-suggestions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .gemini-welcome h2 {
        font-size: 36px;
    }

    .message-bubble {
        max-width: 95%;
        padding: 16px 20px;
    }

    .message-bubble.user {
        padding: 14px 20px;
    }

    .message-bubble.assistant {
        padding: 18px 22px;
    }

    .gemini-input-tools {
        gap: 8px;
        margin-top: 16px;
    }

    .input-tool {
        padding: 8px 12px;
        font-size: 12px;
        gap: 4px;
    }

    .typing-indicator {
        max-width: 95%;
        padding: 16px;
        font-size: 14px;
    }
}

/* Dark theme enhancements */
@media (prefers-color-scheme: dark) {
    .gemini-chat-modal {
        background: #0d1117;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gemini-input {
        border-color: rgba(255, 255, 255, 0.5);
    }
    
    .suggestion-card {
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .message-bubble.user {
        border-color: rgba(88, 166, 255, 0.6);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gemini-chat-modal,
    .suggestion-card,
    .gemini-send-btn,
    .input-tool,
    .gemini-welcome h2 {
        transition: none;
        animation: none !important;
    }

    .typing-dot {
        animation: none;
    }

    @keyframes fadeInUp {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* Final center alignment override */
.gemini-messages {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

.message-bubble {
    align-self: center !important;
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: fit-content !important;
}

.message-bubble.user,
.message-bubble.assistant {
    align-self: center !important;
    text-align: center !important;
}

.typing-indicator {
    align-self: center !important;
}
