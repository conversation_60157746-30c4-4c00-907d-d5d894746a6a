<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist Chat Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fafafa;
            background-image: 
                radial-gradient(circle at 25% 25%, #f0f9ff 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #fef3f2 0%, transparent 50%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: #ffffff;
            border-radius: 24px;
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 0 0 1px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: flex-end;
            background: #ffffff;
        }

        .close-btn {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            color: #64748b;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #f1f5f9;
            color: #475569;
        }

        .welcome-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px 32px;
            text-align: center;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 300;
            color: #1e293b;
            margin-bottom: 16px;
            letter-spacing: -0.025em;
        }

        .welcome-subtitle {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 48px;
            max-width: 480px;
            line-height: 1.6;
            font-weight: 400;
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            width: 100%;
            max-width: 560px;
        }

        .suggestion-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: right;
            position: relative;
        }

        .suggestion-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            transform: translateY(-2px);
        }

        .suggestion-title {
            font-size: 1rem;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .suggestion-desc {
            font-size: 0.875rem;
            color: #64748b;
            line-height: 1.5;
        }

        .input-section {
            padding: 24px;
            background: #ffffff;
            border-top: 1px solid #f1f5f9;
        }

        .input-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 12px 16px;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            transition: all 0.2s ease;
        }

        .input-container:focus-within {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #1e293b;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
            line-height: 1.5;
        }

        .message-input::placeholder {
            color: #94a3b8;
        }

        .send-btn {
            background: #3b82f6;
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .send-btn:hover {
            background: #2563eb;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        .input-tools {
            display: flex;
            gap: 12px;
            margin-top: 16px;
            justify-content: center;
        }

        .tool-btn {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            color: #64748b;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .tool-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            color: #475569;
        }

        .messages-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            display: none;
        }

        .message {
            margin-bottom: 24px;
            animation: messageSlide 0.3s ease;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(16px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 16px 20px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
            font-size: 15px;
            line-height: 1.5;
        }

        .message.user .message-bubble {
            background: #3b82f6;
            color: white;
            border-bottom-left-radius: 6px;
        }

        .message.bot .message-bubble {
            background: #f8fafc;
            color: #1e293b;
            border: 1px solid #e2e8f0;
            border-bottom-right-radius: 6px;
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 24px;
        }

        .typing-bubble {
            display: inline-block;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 16px 20px;
            border-radius: 18px;
            border-bottom-right-radius: 6px;
            color: #64748b;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-left: 8px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #94a3b8;
            border-radius: 50%;
            animation: typingMinimal 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingMinimal {
            0%, 60%, 100% { 
                transform: translateY(0);
                opacity: 0.4;
            }
            30% { 
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        .demo-label {
            position: absolute;
            top: 16px;
            left: 24px;
            background: #3b82f6;
            color: white;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            z-index: 10;
        }

        /* Scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 6px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: #f8fafc;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .messages-area::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        @media (max-width: 768px) {
            .suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">Minimalist</div>
        
        <div class="chat-header">
            <button class="close-btn" onclick="window.close()">×</button>
        </div>

        <div class="welcome-section" id="welcomeSection">
            <h1 class="welcome-title">مرحباً</h1>
            <p class="welcome-subtitle">كيف يمكنني مساعدتك في تحسين استهلاك الطاقة اليوم؟</p>
            
            <div class="suggestions-grid">
                <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                    <div class="suggestion-title">تحليل استهلاك الطاقة</div>
                    <div class="suggestion-desc">احصل على تحليل مفصل لاستهلاك الطاقة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                    <div class="suggestion-title">نصائح توفير الطاقة</div>
                    <div class="suggestion-desc">اكتشف طرق ذكية لتوفير الطاقة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                    <div class="suggestion-title">الطاقة المتجددة</div>
                    <div class="suggestion-desc">تعرف على حلول الطاقة المستدامة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الأجهزة الذكية">
                    <div class="suggestion-title">الأجهزة الذكية</div>
                    <div class="suggestion-desc">استخدم التكنولوجيا لتحسين الكفاءة</div>
                </div>
            </div>
        </div>

        <div class="messages-area" id="messagesArea"></div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يكتب...
            </div>
        </div>

        <div class="input-section">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="اكتب رسالتك هنا..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>→</button>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn">
                    <span>🎤</span>
                    <span>صوتي</span>
                </button>
                <button class="tool-btn">
                    <span>📎</span>
                    <span>مرفق</span>
                </button>
                <button class="tool-btn">
                    <span>🔍</span>
                    <span>بحث</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        const welcomeSection = document.getElementById('welcomeSection');
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            sendBtn.disabled = this.value.trim() === '';
        });

        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const suggestion = this.getAttribute('data-suggestion');
                startChat(suggestion);
            });
        });

        function startChat(message) {
            welcomeSection.style.display = 'none';
            messagesArea.style.display = 'block';
            addMessage(message, 'user');
            showTyping();
            
            setTimeout(() => {
                hideTyping();
                const responses = {
                    'تحليل استهلاك الطاقة': 'ممتاز! سأساعدك في تحليل استهلاك الطاقة:\n\n• مراجعة الفواتير الشهرية\n• قياس استهلاك الأجهزة الرئيسية\n• تحديد أوقات الذروة\n• حساب التكلفة لكل جهاز\n• اقتراح خطة للتحسين\n\nهل لديك فاتورة كهرباء حديثة يمكنني مساعدتك في تحليلها؟',
                    'نصائح توفير الطاقة': 'إليك أهم النصائح العملية لتوفير الطاقة:\n\n💡 استبدل المصابيح بـ LED (توفير 75%)\n🌡️ اضبط التكييف على 24 درجة\n🔌 افصل الأجهزة في وضع الاستعداد\n🪟 استخدم الستائر العازلة\n⏰ استخدم المؤقتات للأجهزة\n\nأي من هذه النصائح تريد التفاصيل عنها؟',
                    'الطاقة المتجددة': 'الطاقة المتجددة استثمار ممتاز! إليك الخيارات:\n\n☀️ الألواح الشمسية:\n- توفير 70-90% من فاتورة الكهرباء\n- عائد الاستثمار خلال 5-7 سنوات\n- عمر افتراضي 25+ سنة\n\n💨 طاقة الرياح (للمناطق المناسبة)\n🔋 أنظمة التخزين\n\nهل تريد حساب التكلفة والعائد لمنزلك؟',
                    'الأجهزة الذكية': 'الأجهزة الذكية تحدث فرقاً كبيراً:\n\n🏠 منظم الحرارة الذكي:\n- توفير 10-15% من تكلفة التدفئة/التبريد\n- تحكم عن بعد\n- جدولة تلقائية\n\n💡 المصابيح الذكية\n🔌 المقابس الذكية\n📱 تطبيقات مراقبة الاستهلاك\n\nأي نوع من الأجهزة يهمك أكثر؟'
                };
                
                const response = responses[message] || 'شكراً لسؤالك! يمكنني مساعدتك في جميع مواضيع الطاقة.';
                addMessage(response, 'bot');
            }, 1500);
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (welcomeSection.style.display !== 'none') {
                startChat(message);
            } else {
                addMessage(message, 'user');
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    addMessage('شكراً لسؤالك! هذا موضوع مهم في مجال الطاقة. دعني أقدم لك معلومات مفيدة...', 'bot');
                }, 1200);
            }

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<div class="message-bubble">${text.replace(/\n/g, '<br>')}</div>`;
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
