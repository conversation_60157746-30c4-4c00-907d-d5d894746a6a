<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Style Chat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: #36393f;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            background: #2f3136;
            padding: 20px;
            border-bottom: 1px solid #40444b;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: #43b581;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .close-btn {
            background: #f04747;
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #d73737;
            transform: scale(1.1);
        }

        .messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #36393f;
        }

        .message {
            margin-bottom: 20px;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #7289da, #5b6eae);
            color: white;
            border-bottom-left-radius: 5px;
        }

        .message.bot .message-bubble {
            background: #40444b;
            color: #dcddde;
            border-bottom-right-radius: 5px;
            border: 1px solid #4f545c;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 20px;
        }

        .typing-bubble {
            display: inline-block;
            background: #40444b;
            padding: 15px 20px;
            border-radius: 20px;
            border-bottom-right-radius: 5px;
            color: #dcddde;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-right: 10px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #7289da;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .input-area {
            background: #40444b;
            padding: 20px;
            border-top: 1px solid #4f545c;
        }

        .input-wrapper {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            background: #484c52;
            border: none;
            border-radius: 25px;
            padding: 15px 20px;
            color: #dcddde;
            font-size: 14px;
            resize: none;
            max-height: 100px;
            min-height: 50px;
            outline: none;
            transition: all 0.3s ease;
        }

        .message-input:focus {
            background: #5865f2;
            color: white;
        }

        .message-input::placeholder {
            color: #72767d;
        }

        .send-btn {
            background: linear-gradient(135deg, #7289da, #5b6eae);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(114, 137, 218, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Scrollbar Styling */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: #2f3136;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #7289da;
            border-radius: 4px;
        }

        .demo-label {
            position: absolute;
            top: 10px;
            left: 20px;
            background: rgba(114, 137, 218, 0.9);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 15px;
            }
            
            .message-bubble {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">Discord Style Demo</div>
        
        <div class="chat-header">
            <div class="chat-title">
                <div class="status-dot"></div>
                مساعد الطاقة
            </div>
            <button class="close-btn" onclick="window.close()">×</button>
        </div>

        <div class="messages-area" id="messagesArea">
            <div class="message bot">
                <div class="message-bubble">
                    مرحباً! أنا مساعدك الذكي في مجال الطاقة 🌟
                    <br>كيف يمكنني مساعدتك اليوم؟
                </div>
                <div class="message-time">الآن</div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يكتب...
            </div>
        </div>

        <div class="input-area">
            <div class="input-wrapper">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="اكتب رسالتك هنا..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            
            sendBtn.disabled = this.value.trim() === '';
        });

        // Send message on Enter
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;

            // Show typing indicator
            showTyping();

            // Simulate bot response
            setTimeout(() => {
                hideTyping();
                const responses = [
                    'شكراً لسؤالك! يمكنني مساعدتك في تحسين استهلاك الطاقة 💡',
                    'هذا سؤال ممتاز! دعني أقدم لك بعض النصائح المفيدة ⚡',
                    'بالطبع! إليك بعض الحلول الذكية لتوفير الطاقة 🌱',
                    'ممتاز! يمكننا تحليل استهلاك الطاقة وتقديم توصيات مخصصة 📊'
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage(randomResponse, 'bot');
            }, 2000);
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div class="message-bubble">${text}</div>
                <div class="message-time">${timeString}</div>
            `;

            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        // Add some demo messages
        setTimeout(() => {
            addMessage('مرحباً، أريد معرفة كيفية توفير الطاقة في منزلي', 'user');
        }, 1000);

        setTimeout(() => {
            showTyping();
        }, 2000);

        setTimeout(() => {
            hideTyping();
            addMessage('ممتاز! إليك أهم النصائح لتوفير الطاقة في المنزل:\n\n• استخدم مصابيح LED\n• اضبط درجة حرارة التكييف على 24°\n• افصل الأجهزة غير المستخدمة\n• استخدم الأجهزة الموفرة للطاقة', 'bot');
        }, 4000);
    </script>
</body>
</html>
