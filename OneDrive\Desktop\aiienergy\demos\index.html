<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخ تجريبية لواجهات المحادثة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
            color: white;
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .demo-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .demo-description {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .demo-features {
            list-style: none;
            margin-bottom: 25px;
            text-align: right;
        }

        .demo-features li {
            padding: 5px 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .demo-features li::before {
            content: "✨";
            margin-left: 10px;
        }

        .demo-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .demo-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .back-btn {
            position: fixed;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 50px;
        }

        .footer p {
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .status-badge {
            display: inline-block;
            background: rgba(37, 211, 102, 0.2);
            color: #25d366;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
            border: 1px solid rgba(37, 211, 102, 0.3);
        }

        .coming-soon {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border-color: rgba(255, 193, 7, 0.3);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .demos-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-card {
                padding: 25px;
            }
            
            .back-btn {
                top: 20px;
                left: 20px;
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-btn">← العودة للموقع</a>

    <div class="container">
        <div class="header">
            <h1>🎨 نسخ تجريبية لواجهات المحادثة</h1>
            <p>اختر من بين التصاميم المختلفة لواجهة المحادثة واكتشف الأنماط الأنيقة والحديثة</p>
        </div>

        <div class="demos-grid">
            <div class="demo-card">
                <div class="status-badge">متاح الآن</div>
                <span class="demo-icon">💜</span>
                <h3 class="demo-title">نمط Discord</h3>
                <p class="demo-description">تصميم حديث مستوحى من Discord مع ألوان أرجوانية أنيقة وتأثيرات متقدمة</p>
                <ul class="demo-features">
                    <li>خلفية داكنة مع تدرجات</li>
                    <li>رسائل مع حواف مستديرة</li>
                    <li>أنيميشن كتابة متقدم</li>
                    <li>تأثيرات hover تفاعلية</li>
                </ul>
                <a href="chat-discord-style.html" class="demo-btn" target="_blank">مشاهدة النسخة التجريبية</a>
            </div>

            <div class="demo-card">
                <div class="status-badge">متاح الآن</div>
                <span class="demo-icon">✨</span>
                <h3 class="demo-title">نمط Glassmorphism</h3>
                <p class="demo-description">تصميم فاخر بتأثيرات زجاجية شفافة وخلفيات متحركة ساحرة</p>
                <ul class="demo-features">
                    <li>تأثيرات زجاجية متقدمة</li>
                    <li>خلفية متحركة ملونة</li>
                    <li>شفافية وتمويه</li>
                    <li>أشكال عائمة متحركة</li>
                </ul>
                <a href="chat-glassmorphism.html" class="demo-btn" target="_blank">مشاهدة النسخة التجريبية</a>
            </div>

            <div class="demo-card">
                <div class="status-badge">متاح الآن</div>
                <span class="demo-icon">💚</span>
                <h3 class="demo-title">نمط WhatsApp</h3>
                <p class="demo-description">تصميم مألوف مستوحى من WhatsApp مع فقاعات الرسائل الكلاسيكية</p>
                <ul class="demo-features">
                    <li>فقاعات رسائل مميزة</li>
                    <li>حالة "تم القراءة"</li>
                    <li>تصميم متجاوب</li>
                    <li>ألوان WhatsApp الأصلية</li>
                </ul>
                <a href="chat-whatsapp-style.html" class="demo-btn" target="_blank">مشاهدة النسخة التجريبية</a>
            </div>

            <div class="demo-card">
                <div class="status-badge coming-soon">قريباً</div>
                <span class="demo-icon">🔵</span>
                <h3 class="demo-title">نمط Telegram</h3>
                <p class="demo-description">تصميم سريع وأنيق مستوحى من Telegram مع انتقالات سلسة</p>
                <ul class="demo-features">
                    <li>انتقالات سريعة</li>
                    <li>تأثيرات بصرية</li>
                    <li>وضع ليلي/نهاري</li>
                    <li>ستيكرز وGIFs</li>
                </ul>
                <button class="demo-btn" disabled style="opacity: 0.6;">قيد التطوير</button>
            </div>

            <div class="demo-card">
                <div class="status-badge coming-soon">قريباً</div>
                <span class="demo-icon">🤖</span>
                <h3 class="demo-title">نمط ChatGPT</h3>
                <p class="demo-description">تصميم مينيمال وأنيق مستوحى من ChatGPT مع ميزات متقدمة</p>
                <ul class="demo-features">
                    <li>تصميم مينيمال</li>
                    <li>كود highlighting</li>
                    <li>نسخ الرسائل</li>
                    <li>تصدير المحادثة</li>
                </ul>
                <button class="demo-btn" disabled style="opacity: 0.6;">قيد التطوير</button>
            </div>

            <div class="demo-card">
                <div class="status-badge coming-soon">قريباً</div>
                <span class="demo-icon">🎭</span>
                <h3 class="demo-title">نمط Neumorphism</h3>
                <p class="demo-description">تصميم ثلاثي الأبعاد ناعم مع ظلال داخلية وخارجية مميزة</p>
                <ul class="demo-features">
                    <li>تصميم ثلاثي الأبعاد</li>
                    <li>ظلال ناعمة</li>
                    <li>ألوان هادئة</li>
                    <li>تأثيرات تفاعلية</li>
                </ul>
                <button class="demo-btn" disabled style="opacity: 0.6;">قيد التطوير</button>
            </div>
        </div>

        <div class="footer">
            <p>🚀 جميع التصاميم قابلة للتخصيص وتتضمن ميزات تفاعلية متقدمة</p>
            <p>💡 اختر التصميم المفضل لديك وسنقوم بتطبيقه على موقعك</p>
        </div>
    </div>
</body>
</html>
